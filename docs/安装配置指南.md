# 安装配置指南

> 本指南面向新入职开发者与运维人员，帮助你在 **30 分钟内** 从零搭建并运行 Tucsenberg 企业展示平台的完整本地与生产环境。

---

## 1. 文档目标与适用范围

- **目标**：提供可执行的逐步安装说明，涵盖必备工具、环境变量、启动脚本及常见问题排查。
- **适用范围**：macOS / Linux / WSL2 Windows 终端；ARM 与 x86_64 架构。

---

## 2. 项目目录结构（精简示例）

```text
flood-defense-site/
├─ apps/
│  ├─ web/            # Next.js 前端
│  └─ content/       # 多语言 MDX 内容库
├─ docker/
│  ├─ docker-compose.yml
│  └─ strapi/
│      └─ strapi.env
├─ .github/
├─ .env.example
└─ pnpm-workspace.yaml
```

---

## 3. 先决条件安装

| 工具 | 版本 | macOS 安装示例 | Ubuntu/WSL 安装示例 |
| ---- | ----- | -------------- | ------------------- |
| Git | ≥ 2.34 | `brew install git` | `sudo apt install git -y` |
| Node.js | 20.x LTS | `brew install volta && volta install node@20` | `curl https://get.volta.sh | bash && volta install node@20` |
| pnpm | 8.x | `corepack enable && corepack prepare pnpm@8 --activate` | 同左 |
| Docker Desktop / Engine | ≥ 24 | 官网下载 dmg | `sudo apt install docker.io docker-compose-plugin -y` |
| Vercel CLI | latest | `pnpm i -g vercel` | 同左 |

> **Tips**：建议开启 Docker Desktop 的 `Use Rosetta`（Apple Silicon）以避免某些 x86 镜像兼容问题。

---

## 4. 克隆仓库与初始化

```bash
# 1. 克隆
$ git clone https://github.com/your-org/flood-defense-site.git
$ cd flood-defense-site

# 2. 安装依赖
$ pnpm install --frozen-lockfile

# 安装 Lingo CLI 为开发依赖
$ pnpm add -D @lingo/cli

# 在 package.json scripts 添加
"lingo:push": "lingo push",
"lingo:pull": "lingo pull"
```

---

## 5. 环境变量 `.env` 模板

复制根目录的 `.env.example`：

```bash
$ cp .env.example .env
```

| 变量 | 说明 | 示例 |
| ---- | ---- | ---- |
| NEXT_PUBLIC_API_URL | 浏览器可见 API 地址 | `http://localhost:3000` |
| RESEND_API_KEY | 邮件服务 API Key | `re_123456789` |
| NEXT_PRIVATE_API_KEY | 内部 API 鉴权密钥 | 自行生成 32 字节随机串 |
| APP_KEYS / JWT_SECRET | Strapi 安全密钥 | 自动生成或手动填写 |
| LINGO_API_KEY | Lingo.dev 认证令牌 | `ln_live_xxxxxxxxxxxxxxxxx` |

> 修改端口或密码请同时在 `docker/docker-compose.yml` 中保持一致。

> 获得 API Key：登录 Lingo.dev Dashboard → Settings → API。请在 Vercel / GitHub Actions Secrets 中分别配置。

---

## 6. 初始化内容层（MDX + Contentlayer）

```bash
# 1. 创建内容目录（仅首次）
$ mkdir -p content/en/products
$ touch content/en/products/aqua-dam.mdx

# 2. 编辑 MDX 并添加 Front-matter
# 3. 运行开发服务器
$ pnpm dev  # next dev + contentlayer build
```

`.contentlayer` 目录将生成类型安全 JSON，编辑时自动热更新。

---

## 7. 启动前端：Next.js

```bash
$ pnpm dev  # 默认在 apps/web 目录下运行 next dev
```

- 打开 `http://localhost:3000` 即可看到网站。
- 编辑 `apps/web/app/page.tsx` 自动热更新。

---

## 8. 常见安装问题与解决办法

| 症状 | 可能原因 | 解决措施 |
| ---- | -------- | -------- |
| `Error: connect ECONNREFUSED localhost:5432` | Postgres 未启动或端口被占用 | `docker ps` 检查容器；更换端口后更新 `.env` |
| `Strapi Error APP_KEYS not defined` | 缺失安全变量 | 在 `.env` 添加 `APP_KEYS=...`，重启容器 |
| 前端无法拉取图片 | Strapi Media 需要 CORS | 进入 Strapi → Settings → CORS 允许 `http://localhost:3000` |
| 生成型 AI 报 `Quota exceeded` | 未配置 RESEND 或 KEY 失效 | 申请免费额度或升级套餐 |

---

## 9. 生产部署快速脚本

```yaml
# .github/workflows/deploy.yml 片段
steps:
  - name: Setup Node
    uses: actions/setup-node@v4
    with:
      node-version: 20
  - run: corepack enable && corepack prepare pnpm@8 --activate
  - run: pnpm install --frozen-lockfile
  - run: pnpm lingo:push
  - run: pnpm test
  - run: pnpm build
  - uses: amondnet/vercel-action@v25
    with:
      vercel-token: ${{ secrets.VERCEL_TOKEN }}
      vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
      vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
```

> 部署成功后，Vercel 将为每次 PR 生成 Preview URL，Contentlayer 会根据最新 MDX 重新构建。

---

## 10. 版本升级与数据备份

- **依赖升级**：启用 Renovate，每周创建 PR；本地手动 `pnpm up --latest` 前先跑 `pnpm test`。
- **数据库备份**：Postgres 使用 `pg_dump` 每日 cron 备份到 S3 Glacier。
- **Strapi 文件**：若使用本地存储，启用 `aws-s3-storage` 插件；CDN 场景直接备份对象存储。

---

## 11. 参考链接

- Next.js Docs: <https://nextjs.org/docs>
- Strapi Docs: <https://docs.strapi.io>
- pnpm: <https://pnpm.io>
- Render Docs: <https://render.com/docs>
- Vercel CLI: <https://vercel.com/docs/cli> 