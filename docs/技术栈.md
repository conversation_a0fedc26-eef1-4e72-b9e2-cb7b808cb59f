# 技术栈与安装配置指南

👉 **安装与本地/生产环境配置请参阅 [《安装配置指南》](./安装配置指南.md)**

> 本指南用于快速搭建、配置并部署 "Tucsenberg 防洪设备企业展示平台" 所需的完整技术栈。所有版本均基于长期支持 (LTS) 或官方稳定版本，确保未来两年以上的升级兼容性。

---

## 1. 技术栈总览

| 层次 | 技术 / 服务 | 稳定版本 | 作用 |
| ---- | ----------- | -------- | ---- |
| 运行时 | **Node.js** | 20.x LTS | 运行 Next.js 服务器 |
| 包管理 | **pnpm** | 8.x | 快速且一致的依赖安装 |
| 前端框架 | **Next.js** | 14.x | React 基础的全栈框架，支持 App Router、ISR、SWR |
| 内容层 | **MDX** + **Contentlayer** | MDX 2.x / Contentlayer 0.4.x | Git-based 内容管理与类型安全解析 |
| 本地化 | **Lingo.dev CLI & SDK** | latest | 自动翻译 UI & MDX 内容 |
| UI & 样式 | **React 18.x** + **TypeScript 5.x** + **Tailwind CSS 3.x** | — | 组件开发、静态类型、安全快速样式 |
| 国际化 | **next-i18next** | 15.x | 页面级多语言支持，兼容 Next.js App Router |
| 部署 & CDN | **Vercel** | 最新 | 全球边缘节点、自动 SSL、CI/CD |

> 建议使用 **VS Code + Volta** 管理本地 Node 版本，保证跨平台一致性。

---

## 2. 系统架构图

```mermaid
flowchart TD
    subgraph Client
        A[浏览器 / 移动设备]
    end

    subgraph Frontend[Next.js 14 App]
        A -->|HTTP(S)| B[Next.js Edge Functions]
        B -->|文件系统加载| C[MDX + Contentlayer]
    end

    C -->|Git 拉取| G[Git Repository]
    B -->|翻译请求| D[Lingo.dev API]
    G -->|CI push/pull| D

    subgraph Deploy[部署网络层]
        B -->|静态资源 CDN| CDN[Vercel Edge Network]
    end
```

---

## 3. 依赖版本锁定策略

1. 使用 `pnpm-workspace.yaml` 锁定包管理，防止版本漂移。
2. 采用 `.nvmrc` 或 Volta(`package.json` engines 字段) 指定 Node 20.x。
3. 在 `package.json` 使用 `"type": "module"` 并固定次要版本 (例如 `next": "14.0.x"`) 以避免破坏性变更。
4. 通过 Renovate / Dependabot 自动提出安全升级 PR。
5. 安装 `@lingo/cli` 作为 devDependency，并在 `package.json` scripts 添加 `lingo:push` / `lingo:pull` 方便本地与 CI 调用。

---

## 4. 安装前置需求

- Git ≥ 2.34
- Node.js 20.x (建议 Volta 管理)
- pnpm 8.x 全局 (`corepack enable && corepack prepare pnpm@8 --activate`)
- Docker Desktop ≥ 4.24 (含 Compose v2)

---

## 5. 本地开发环境搭建

```bash
# 1. 克隆仓库
$ <NAME_EMAIL>:your-org/flood-defense-site.git && cd flood-defense-site

# 2. 安装依赖
$ pnpm install

# 3. 启动内容层监听（Contentlayer）
$ pnpm dev        # next dev 会自动触发 contentlayer build

# 4. 打开 http://localhost:3000 查看网站
```

> 首次运行时 Contentlayer 会在 `.contentlayer` 目录生成类型安全的 JSON 与 TS 类型。

---

## 6. 内容目录与 Front-matter 规范

项目根目录创建 `content/`，每种语言一个子目录：

```text
content/
├─ en/
│  └─ products/
│      └─ aqua-dam.mdx
├─ zh-CN/
│  └─ products/
│      └─ aqua-dam.mdx
```

MDX Front-matter 示例：

```mdx
---
title: "AquaDam 防洪墙"
slug: aqua-dam
category: barrier
specs:
  height: "1m"
  length: "10m"
images:
  - /images/aqua-dam/hero.jpg
---

正文内容支持 <Warning /> 组件。
```

运行 `pnpm lingo:push` 将新增或修改的 MDX 提交至 Lingo.dev；CI 自动 `lingo pull` 生成翻译文件。

---

## 7. 国际化配置指南 (next-i18next)

```js
// next-i18next.config.js
module.exports = {
  i18n: {
    defaultLocale: 'en',
    locales: ['en', 'zh-CN', 'ja', 'es'],
  },
  reloadOnPrerender: process.env.NODE_ENV === 'development',
};
```

在 `app/layout.tsx` (App Router) 中引入 `appWithTranslation`；在页面使用 `useTranslation()` 获取词条。构建时 JSON 语言包由 `locales/*/*.json` 提供，与 Strapi 本地化内容解耦。

---

## 8. 部署流程概览

### 8.1 站点部署 (Vercel)

```bash
$ vercel login
$ vercel link
$ vercel env add LINGO_API_KEY ****
$ vercel --prod
```

- CI 阶段执行 `pnpm lingo:push` 推送待翻译字符串，随后 `vercel build` 自动静态生成各语言页面。  
- **Incremental Static Regeneration (ISR)** 依据 `revalidate` 参数自动更新缓存，无需后端 API。  

### 8.2 自定义域名 & CDN

- 在 Cloudflare / Alibaba Cloud DNS 将 `www` 解析到 Vercel 提供的 CNAME。  
- 启用 **HTTP/3**、自动压缩与图像优化 (Vercel OG / Cloudflare Polish)。

### 8.3 后端 (Strapi)

```bash
# Render 示例
# 1. 创建 PostgreSQL 实例 (15)
# 2. 部署 Docker image，设置环境变量 DATABASE_URL, APP_KEYS, JWT_SECRET 等
```

- 配置 **Cron** 定时任务 (Strapi 定期触发 revalidation webhook)。
- 打开 **CORS** 允许 Vercel 域名。

---

## 9. 常见问题 FAQ

| 问题 | 解决办法 |
| ---- | -------- |
| 页面构建时报 `MDX: Unexpected token` | 检查 Front-matter 是否闭合，或 JSX 标签未正确导入 |
| 语言文件未同步 | 确认 CI 已执行 `lingo pull` 且 `locales/*/*.json` 更新 |
| UI 字符串缺失占位符 | 在 Dashboard 设置 **Placeholder Validation**，重新生成翻译 |
| ISR 页面未更新 | `next.config.js` 中增加 `revalidate` 或在 Vercel Dashboard 手动触发 `Invalidate Cache` |

---

> **维护建议**：每月检查依赖安全公告；Strapi 小版本升级可直接执行 `pnpm strapi:update`；Next.js 大版本升级需关注 Breaking Changes 文档。

---

**文档最后更新**：{{DATE}} 

---

## 10. 功能集成与第三方服务

> 本章节说明前端、CMS、第三方 API 与监控体系之间的集成方式，确保功能完整且安全可运维。

### 10.1 数据流

1. **内容读取**：Next.js 在构建/ISR 时通过 **Contentlayer** 读取 `content/**` MDX 并生成静态 JSON，运行时直接从文件系统获取。  
2. **内容翻译**：CI Push 时 `@lingo/cli` 扫描新增/修改 MDX & `locales` JSON，自动创建 PR 供审校。  
3. **图像优化**：媒体文件存储于 `public/images`；Next.js `<Image>` 组件配合 Vercel Edge CDN 进行按需裁剪与懒加载。

### 10.2 表单与邮件服务

| 功能 | 实现路径 | 关键依赖 |
| ---- | -------- | -------- |
| 客户询价表单 | `/app/contact/page.tsx` → `/pages/api/contact.ts` | **Resend** / **SendGrid** |
| 数据入库 | 保留在 GitHub Issues / Airtable / Supabase | 轻量 CRM 集成 |
| 自动回复邮件 | API Route 调用 `await resend.emails.send()` | `RESEND_API_KEY` |

### 10.3 搜索服务

- 使用 **Algolia DocSearch** 或自托管 **Meilisearch**：CI 根据 Contentlayer 输出自动推送索引。

### 10.4 分析与埋点

| 方案 | 部署 | 隐私合规 |
| ---- | ---- | -------- |
| **Plausible** | 自托管 Docker 或 cloud.plausible.io | 默认匿名，无 Cookie |
| **Google Analytics v4** | gtag.js 脚本 | 启用 IP 匿名化，符合 GDPR |
| **Sentry** | `@sentry/nextjs` | 仅在生产构建启用 |

### 10.5 CI / CD 流程

```yaml
# .github/workflows/deploy.yml (示例节选)
name: CI
on:
  push:
    branches: [main]

jobs:
  build-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
        with:
          version: ^8
      - run: pnpm install --frozen-lockfile
      - run: pnpm test
      - run: pnpm build
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
```

- Strapi Docker 镜像通过 **GHCR** 自动构建并推送；Render/Fly.io 监听镜像更新自动重启。
- 可选：在 Strapi 发布内容时触发 GitHub Action `repository_dispatch` 刷新前端页面缓存。

### 10.6 安全与权限

- 静态站点无后端接口暴露，主要关注 **API Routes** 需校验 `NEXT_PRIVATE_API_KEY`。  
- 环境变量通过 Vercel Environments 管理，防止泄漏。

### 10.7 监控与日志

| 维度 | 工具 | 说明 |
| ---- | ---- | ---- |
| 前端性能 | Vercel Web Vitals | 自动采集核心指标 CLS/LCP/FID |
| 前端错误 | Sentry Browser | Source Map 上传自动定位 |
| 后端日志 | Render Log Stream → Logtail | 日志检索与报警 |
| 数据库监控 | pg_stat_statements + Grafana | 查询耗时与慢 SQL 报告 |

---

> **集成 Tips**：所有第三方服务均应位于同一区域节点（如 AWS us-east-1），以降低网络时延；同时使用 Infrastructure-as-Code (Terraform) 描述资源，保证可重复部署。 