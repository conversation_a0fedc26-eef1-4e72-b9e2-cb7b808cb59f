{"version": 3, "file": "yup.module.js", "sources": ["../src/yup.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport {\n  FieldError,\n  FieldValues,\n  Resolver,\n  appendErrors,\n} from 'react-hook-form';\nimport * as Yup from 'yup';\n\n/**\n * Why `path!` ? because it could be `undefined` in some case\n * https://github.com/jquense/yup#validationerrorerrors-string--arraystring-value-any-path-string\n */\nconst parseErrorSchema = (\n  error: Yup.ValidationError,\n  validateAllFieldCriteria: boolean,\n) => {\n  return (error.inner || []).reduce<Record<string, FieldError>>(\n    (previous, error) => {\n      if (!previous[error.path!]) {\n        previous[error.path!] = { message: error.message, type: error.type! };\n      }\n\n      if (validateAllFieldCriteria) {\n        const types = previous[error.path!].types;\n        const messages = types && types[error.type!];\n\n        previous[error.path!] = appendErrors(\n          error.path!,\n          validateAllFieldCriteria,\n          previous,\n          error.type!,\n          messages\n            ? ([] as string[]).concat(messages as string[], error.message)\n            : error.message,\n        ) as FieldError;\n      }\n\n      return previous;\n    },\n    {},\n  );\n};\n\nexport function yupResolver<TFieldValues extends FieldValues>(\n  schema:\n    | Yup.ObjectSchema<TFieldValues>\n    | ReturnType<typeof Yup.lazy<Yup.ObjectSchema<TFieldValues>>>,\n  schemaOptions: Parameters<(typeof schema)['validate']>[1] = {},\n  resolverOptions: {\n    /**\n     * @default async\n     */\n    mode?: 'async' | 'sync';\n    /**\n     * Return the raw input values rather than the parsed values.\n     * @default false\n     */\n    raw?: boolean;\n  } = {},\n): Resolver<Yup.InferType<typeof schema>> {\n  return async (values, context, options) => {\n    try {\n      if (schemaOptions.context && process.env.NODE_ENV === 'development') {\n        // eslint-disable-next-line no-console\n        console.warn(\n          \"You should not used the yup options context. Please, use the 'useForm' context object instead\",\n        );\n      }\n\n      const result = await schema[\n        resolverOptions.mode === 'sync' ? 'validateSync' : 'validate'\n      ](\n        values,\n        Object.assign({ abortEarly: false }, schemaOptions, { context }),\n      );\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        values: resolverOptions.raw ? values : result,\n        errors: {},\n      };\n    } catch (e: any) {\n      if (!e.inner) {\n        throw e;\n      }\n\n      return {\n        values: {},\n        errors: toNestErrors(\n          parseErrorSchema(\n            e,\n            !options.shouldUseNativeValidation &&\n              options.criteriaMode === 'all',\n          ),\n          options,\n        ),\n      };\n    }\n  };\n}\n"], "names": ["yupResolver", "schema", "schemaOptions", "resolverOptions", "values", "context", "options", "Promise", "resolve", "process", "env", "NODE_ENV", "console", "warn", "mode", "Object", "assign", "abort<PERSON><PERSON><PERSON>", "then", "result", "shouldUseNativeValidation", "validateFieldsNatively", "raw", "errors", "_catch", "e", "inner", "toNestErrors", "error", "validateAllFieldCriteria", "criteriaMode", "reduce", "previous", "path", "message", "type", "types", "messages", "appendErrors", "concat", "reject"], "mappings": "8HA4CgB,SAAAA,EACdC,EAGAC,EACAC,GAYA,YAbAD,IAAAA,IAAAA,EAA4D,CAAE,QAC9DC,IAAAA,IAAAA,EAUI,IAEUC,SAAAA,EAAQC,EAASC,GAAW,IAAA,OAAAC,QAAAC,iCAElCN,EAAcG,SAAoC,gBAAzBI,QAAQC,IAAIC,UAEvCC,QAAQC,KACN,iGAEHN,QAAAC,QAEoBP,EACM,SAAzBE,EAAgBW,KAAkB,eAAiB,YAEnDV,EACAW,OAAOC,OAAO,CAAEC,YAAY,GAASf,EAAe,CAAEG,QAAAA,MACvDa,cALKC,GASN,OAFAb,EAAQc,2BAA6BC,EAAuB,CAAE,EAAEf,GAEzD,CACLF,OAAQD,EAAgBmB,IAAMlB,EAASe,EACvCI,OAAQ,CAAA,EACR,6DArBoCC,CAAA,WAsB/BC,GACP,IAAKA,EAAEC,MACL,MAAMD,EAGR,MAAO,CACLrB,OAAQ,CAAE,EACVmB,OAAQI,GA5EdC,EA8EUH,EA7EVI,GA8EWvB,EAAQc,2BACkB,QAAzBd,EAAQwB,cA7EZF,EAAMF,OAAS,IAAIK,OACzB,SAACC,EAAUJ,GAKT,GAJKI,EAASJ,EAAMK,QAClBD,EAASJ,EAAMK,MAAS,CAAEC,QAASN,EAAMM,QAASC,KAAMP,EAAMO,OAG5DN,EAA0B,CAC5B,IAAMO,EAAQJ,EAASJ,EAAMK,MAAOG,MAC9BC,EAAWD,GAASA,EAAMR,EAAMO,MAEtCH,EAASJ,EAAMK,MAASK,EACtBV,EAAMK,KACNJ,EACAG,EACAJ,EAAMO,KACNE,EACK,GAAgBE,OAAOF,EAAsBT,EAAMM,SACpDN,EAAMM,QAEd,CAEA,OAAOF,CACT,EACA,KAwDM1B,IAnFe,IACvBsB,EACAC,CAoFE,GACF,CAAC,MAAAJ,GAAA,OAAAlB,QAAAiC,OAAAf,EACH,CAAA,CAAA"}