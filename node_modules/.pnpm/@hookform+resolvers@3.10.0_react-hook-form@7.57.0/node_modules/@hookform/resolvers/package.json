{"name": "@hookform/resolvers", "amdName": "hookformResolvers", "version": "3.10.0", "description": "React Hook Form validation resolvers: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Class Validator, io-ts, Nope, computed-types, TypeBox, arktype, Typanion, Effect-TS and VineJS", "main": "dist/resolvers.js", "module": "dist/resolvers.module.js", "umd:main": "dist/resolvers.umd.js", "source": "src/index.ts", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "umd": "./dist/resolvers.umd.js", "import": "./dist/resolvers.mjs", "require": "./dist/resolvers.js"}, "./zod": {"types": "./zod/dist/index.d.ts", "umd": "./zod/dist/zod.umd.js", "import": "./zod/dist/zod.mjs", "require": "./zod/dist/zod.js"}, "./typebox": {"types": "./typebox/dist/index.d.ts", "umd": "./typebox/dist/typebox.umd.js", "import": "./typebox/dist/typebox.mjs", "require": "./typebox/dist/typebox.js"}, "./yup": {"types": "./yup/dist/index.d.ts", "umd": "./yup/dist/yup.umd.js", "import": "./yup/dist/yup.mjs", "require": "./yup/dist/yup.js"}, "./joi": {"types": "./joi/dist/index.d.ts", "umd": "./joi/dist/joi.umd.js", "import": "./joi/dist/joi.mjs", "require": "./joi/dist/joi.js"}, "./vest": {"types": "./vest/dist/index.d.ts", "umd": "./vest/dist/vest.umd.js", "import": "./vest/dist/vest.mjs", "require": "./vest/dist/vest.js"}, "./superstruct": {"types": "./superstruct/dist/index.d.ts", "umd": "./superstruct/dist/superstruct.umd.js", "import": "./superstruct/dist/superstruct.mjs", "require": "./superstruct/dist/superstruct.js"}, "./class-validator": {"types": "./class-validator/dist/index.d.ts", "umd": "./class-validator/dist/class-validator.umd.js", "import": "./class-validator/dist/class-validator.mjs", "require": "./class-validator/dist/class-validator.js"}, "./io-ts": {"types": "./io-ts/dist/index.d.ts", "umd": "./io-ts/dist/io-ts.umd.js", "import": "./io-ts/dist/io-ts.mjs", "require": "./io-ts/dist/io-ts.js"}, "./nope": {"types": "./nope/dist/index.d.ts", "umd": "./nope/dist/nope.umd.js", "import": "./nope/dist/nope.mjs", "require": "./nope/dist/nope.js"}, "./computed-types": {"types": "./computed-types/dist/index.d.ts", "umd": "./computed-types/dist/computed-types.umd.js", "import": "./computed-types/dist/computed-types.mjs", "require": "./computed-types/dist/computed-types.js"}, "./typanion": {"types": "./typanion/dist/index.d.ts", "umd": "./typanion/dist/typanion.umd.js", "import": "./typanion/dist/typanion.mjs", "require": "./typanion/dist/typanion.js"}, "./ajv": {"types": "./ajv/dist/index.d.ts", "umd": "./ajv/dist/ajv.umd.js", "import": "./ajv/dist/ajv.mjs", "require": "./ajv/dist/ajv.js"}, "./arktype": {"types": "./arktype/dist/index.d.ts", "umd": "./arktype/dist/arktype.umd.js", "import": "./arktype/dist/arktype.mjs", "require": "./arktype/dist/arktype.js"}, "./valibot": {"types": "./valibot/dist/index.d.ts", "umd": "./valibot/dist/valibot.umd.js", "import": "./valibot/dist/valibot.mjs", "require": "./valibot/dist/valibot.js"}, "./typeschema": {"types": "./typeschema/dist/index.d.ts", "umd": "./typeschema/dist/typeschema.umd.js", "import": "./typeschema/dist/typeschema.mjs", "require": "./typeschema/dist/typeschema.js"}, "./effect-ts": {"types": "./effect-ts/dist/index.d.ts", "umd": "./effect-ts/dist/effect-ts.umd.js", "import": "./effect-ts/dist/effect-ts.mjs", "require": "./effect-ts/dist/effect-ts.js"}, "./vine": {"types": "./vine/dist/index.d.ts", "umd": "./vine/dist/vine.umd.js", "import": "./vine/dist/vine.mjs", "require": "./vine/dist/vine.js"}, "./fluentvalidation-ts": {"types": "./fluentvalidation-ts/dist/index.d.ts", "umd": "./fluentvalidation-ts/dist/fluentvalidation-ts.umd.js", "import": "./fluentvalidation-ts/dist/fluentvalidation-ts.mjs", "require": "./fluentvalidation-ts/dist/fluentvalidation-ts.js"}, "./package.json": "./package.json", "./*": "./*"}, "files": ["dist", "yup/package.json", "yup/src", "yup/dist", "zod/package.json", "zod/src", "zod/dist", "vest/package.json", "vest/src", "vest/dist", "joi/package.json", "joi/src", "joi/dist", "superstruct/package.json", "superstruct/src", "superstruct/dist", "class-validator/package.json", "class-validator/src", "class-validator/dist", "io-ts/package.json", "io-ts/src", "io-ts/dist", "nope/package.json", "nope/src", "nope/dist", "computed-types/package.json", "computed-types/src", "computed-types/dist", "typanion/package.json", "typanion/src", "typanion/dist", "ajv/package.json", "ajv/src", "ajv/dist", "typebox/package.json", "typebox/src", "typebox/dist", "arktype/package.json", "arktype/src", "arktype/dist", "valibot/package.json", "valibot/src", "valibot/dist", "typeschema/package.json", "typeschema/src", "typeschema/dist", "effect-ts/package.json", "effect-ts/src", "effect-ts/dist", "effect-ts/package.json", "effect-ts/src", "effect-ts/dist", "vine/package.json", "vine/src", "vine/dist", "fluentvalidation-ts/package.json", "fluentvalidation-ts/src", "fluentvalidation-ts/dist"], "publishConfig": {"access": "public"}, "scripts": {"prepare": "run-s build:src", "build": "cross-env npm-run-all --parallel 'build:*'", "build:src": "microbundle build --globals react-hook-form=ReactHookForm", "build:zod": "microbundle --cwd zod --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm", "build:yup": "microbundle --cwd yup --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm", "build:joi": "microbundle --cwd joi --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm", "build:superstruct": "microbundle --cwd superstruct --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm", "build:io-ts": "microbundle --cwd io-ts --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm,fp-ts/Either=Either,fp-ts/function=_function,fp-ts/Option=Option,fp-ts/ReadonlyArray=ReadonlyArray,fp-ts/Semigroup=Semigroup,fp-ts/ReadonlyRecord=ReadonlyRecord", "build:vest": "microbundle --cwd vest --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm,vest/promisify=promisify", "build:class-validator": "microbundle --cwd class-validator --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm", "build:nope": "microbundle --cwd nope --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm", "build:computed-types": "microbundle --cwd computed-types --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm", "build:typanion": "microbundle --cwd typanion --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm", "build:ajv": "microbundle --cwd ajv --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm", "build:typebox": "microbundle --cwd typebox --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm,@sinclair/typebox/value=value,@sinclair/typebox/compiler=compiler", "build:arktype": "microbundle --cwd arktype --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm", "build:valibot": "microbundle --cwd valibot --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm", "build:typeschema": "microbundle --cwd typeschema --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm,@typeschema/main=main", "build:effect-ts": "microbundle --cwd effect-ts --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm,effect=Effect,effect/SchemaAST=EffectSchemaAST,effect/ParseResult=EffectParseResult", "build:vine": "microbundle --cwd vine --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm,@vinejs/vine=vine", "build:fluentvalidation-ts": "microbundle --cwd fluentvalidation-ts --globals @hookform/resolvers=hookformResolvers,react-hook-form=ReactHookForm", "postbuild": "node ./config/node-13-exports.js && check-export-map", "lint": "bunx @biomejs/biome check --write --vcs-use-ignore-file=true .", "lint:types": "tsc", "test": "vitest run", "test:watch": "vitest watch", "check:all": "npm-run-all --parallel lint:* test", "csb:install": "pnpx replace-json-property@1.9.0 package.json prepare \"node -e 'process.exit(0)'\" && pnpm i --no-frozen-lockfile", "csb:build": "cross-env npm-run-all --sequential 'build:*'"}, "keywords": ["scheme", "validation", "scheme-validation", "hookform", "react-hook-form", "yup", "joi", "superstruct", "typescript", "zod", "vest", "class-validator", "io-ts", "effect-ts", "nope", "computed-types", "typanion", "ajv", "TypeBox", "arktype", "typeschema", "vine", "fluentvalidation-ts"], "repository": {"type": "git", "url": "git+https://github.com/react-hook-form/resolvers.git"}, "author": "bluebill1049 <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/react-hook-form/resolvers/issues"}, "homepage": "https://react-hook-form.com", "devDependencies": {"@sinclair/typebox": "^0.32.34", "@testing-library/dom": "^10.3.1", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^20.14.9", "@types/react": "^18.3.3", "@typeschema/core": "^0.13.2", "@typeschema/main": "^0.13.10", "@typeschema/zod": "^0.13.3", "@vinejs/vine": "^2.1.0", "@vitejs/plugin-react": "^4.3.1", "ajv": "^8.16.0", "ajv-errors": "^3.0.0", "arktype": "2.0.0-dev.26", "check-export-map": "^1.3.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "computed-types": "^1.11.2", "cross-env": "^7.0.3", "effect": "^3.10.3", "fluentvalidation-ts": "^3.2.0", "fp-ts": "^2.16.7", "io-ts": "^2.2.21", "io-ts-types": "^0.5.19", "joi": "^17.13.3", "jsdom": "^24.1.0", "lefthook": "^1.6.18", "microbundle": "^0.15.1", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "nope-validator": "^1.0.4", "npm-run-all": "^4.1.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.1", "reflect-metadata": "^0.2.2", "superstruct": "^1.0.4", "typanion": "^3.14.0", "typescript": "^5.5.3", "valibot": "^1.0.0-beta.0", "vest": "^5.3.0", "vite": "^5.3.3", "vite-tsconfig-paths": "^4.3.2", "vitest": "^1.6.0", "yup": "^1.4.0", "zod": "^3.23.8"}, "peerDependencies": {"react-hook-form": "^7.0.0"}}