import{validateFieldsNatively as e,toNestErrors as r}from"@hookform/resolvers";import{errors as s}from"@vinejs/vine";import{appendErrors as o}from"react-hook-form";const t=(e,r)=>{const s={};for(;e.length;){const t=e[0],a=t.field;if(a in s||(s[a]={message:t.message,type:t.rule}),r){const{types:e}=s[a],i=e&&e[t.rule];s[a]=o(a,r,s,t.rule,i?[...i,t.message]:t.message)}e.shift()}return s},a=(o,a,i={})=>async(n,l,m)=>{try{const r=await o.validate(n,a);return m.shouldUseNativeValidation&&e({},m),{errors:{},values:i.raw?n:r}}catch(e){if(e instanceof s.E_VALIDATION_ERROR)return{values:{},errors:r(t(e.messages,!m.shouldUseNativeValidation&&"all"===m.criteriaMode),m)};throw e}};export{a as vineResolver};
//# sourceMappingURL=vine.modern.mjs.map
