import{toNestErrors as r,validateFieldsNatively as e}from"@hookform/resolvers";import o from"vest/promisify";var t=function(r,e){var o={};for(var t in r)o[t]||(o[t]={message:r[t][0],type:""}),e&&(o[t].types=r[t].reduce(function(r,e,o){return(r[o]=e)&&r},{}));return o},s=function(s,n,i){return void 0===i&&(i={}),function(n,a,u){try{var m=function(o){return o.hasErrors()?{values:{},errors:r(t(o.getErrors(),!u.shouldUseNativeValidation&&"all"===u.criteriaMode),u)}:(u.shouldUseNativeValidation&&e({},u),{values:n,errors:{}})};return Promise.resolve("sync"===i.mode?m(s(n,u.names,a)):Promise.resolve(o(s)(n,u.names,a)).then(m))}catch(r){return Promise.reject(r)}}};export{s as vestResolver};
//# sourceMappingURL=vest.module.js.map
