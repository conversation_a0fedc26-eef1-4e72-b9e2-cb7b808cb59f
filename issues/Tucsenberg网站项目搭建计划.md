# Tucsenberg防洪设备企业展示平台 - 项目搭建执行计划

## 📋 项目概览

**项目名称：** Tucsenberg防洪设备企业展示平台  
**项目类型：** 企业展示网站（简化单仓库结构）  
**预计工期：** 10-14个工作日  
**技术栈：** Next.js 14 + TypeScript + Tailwind CSS + Contentlayer + next-i18next

## 🎯 项目目标

- 构建现代化的防洪设备企业展示平台
- 支持4种语言（英语、中文、日语、西班牙语）
- 实现产品展示、企业介绍、客户询价功能
- 优化SEO和用户体验
- 部署到Vercel生产环境

## 📁 推荐项目结构

```
tucsenberg-website/
├─ src/
│  ├─ app/                    # Next.js 14 App Router
│  │  ├─ [locale]/           # 国际化路由
│  │  ├─ api/                # API路由
│  │  └─ globals.css         # 全局样式
│  ├─ components/            # React组件库
│  ├─ lib/                   # 工具函数
│  └─ types/                 # TypeScript类型
├─ content/                  # MDX内容文件
├─ public/                   # 静态资源
├─ locales/                  # 国际化JSON文件
├─ docs/                     # 项目文档
└─ [配置文件]
```

## 🚀 详细执行计划

### 第一阶段：基础项目结构搭建（2-3天）

#### 步骤1.1：创建项目目录结构
- **操作文件：** 根目录完整文件夹结构
- **执行方式：** 使用mkdir命令批量创建
- **预期结果：** 完整的项目骨架就绪
- **验证标准：** 所有必要目录存在且结构正确

#### 步骤1.2：配置package.json
- **文件路径：** `./package.json`
- **涉及内容：** 项目元信息、依赖项、脚本命令
- **代码行数：** 约80-100行
- **关键依赖：**
  - next@^14.0.0
  - react@^18.0.0
  - typescript@^5.0.0
  - tailwindcss@^3.0.0
  - contentlayer@^0.4.0
  - next-i18next@^15.0.0
  - @tailwindcss/typography
  - resend (邮件服务)
  - zod (表单验证)
- **预期结果：** 依赖安装成功，pnpm install正常运行

#### 步骤1.3：TypeScript配置
- **文件路径：** `./tsconfig.json`
- **涉及内容：** 编译选项、路径映射、类型检查规则
- **代码行数：** 约30-40行
- **关键配置：**
  - 路径别名：@/* -> ./src/*
  - 严格模式启用
  - Next.js类型支持
- **预期结果：** TypeScript环境就绪，类型检查正常

#### 步骤1.4：Next.js配置
- **文件路径：** `./next.config.js`
- **涉及内容：** 国际化配置、Contentlayer集成、图片优化
- **代码行数：** 约40-50行
- **关键配置：**
  - i18n路由配置
  - Contentlayer插件集成
  - 图片域名白名单
- **预期结果：** Next.js开发服务器可启动（pnpm dev）

#### 步骤1.5：Tailwind CSS配置
- **文件路径：** `./tailwind.config.js`, `./src/app/globals.css`
- **涉及内容：** 主题配置、自定义样式、响应式断点
- **代码行数：** tailwind.config.js约50行，globals.css约30行
- **关键配置：**
  - 企业品牌色彩系统
  - 自定义字体配置
  - 响应式断点
- **预期结果：** 样式系统就绪，可使用Tailwind类名

### 第二阶段：内容管理系统（2天）

#### 步骤2.1：Contentlayer配置
- **文件路径：** `./contentlayer.config.js`
- **涉及内容：** 文档类型定义、字段验证、计算字段
- **代码行数：** 约60-80行
- **关键配置：**
  - Product内容类型（产品信息）
  - Page内容类型（静态页面）
  - 多语言字段支持
- **预期结果：** .contentlayer目录生成，类型文件可用

#### 步骤2.2：内容类型定义
- **文件路径：** `./src/types/content.ts`
- **涉及内容：** TypeScript接口定义
- **代码行数：** 约40-50行
- **关键类型：**
  - ProductMeta接口
  - PageMeta接口
  - LocaleContent接口
- **预期结果：** 类型安全的内容访问

#### 步骤2.3：创建示例MDX内容
- **文件路径：** 
  - `./content/en/products/aqua-dam.mdx`
  - `./content/zh-CN/products/aqua-dam.mdx`
- **涉及内容：** Front-matter定义、产品描述、技术规格
- **代码行数：** 每个文件约50-100行
- **关键内容：**
  - 产品标题、描述、规格
  - 图片路径配置
  - SEO元数据
- **预期结果：** 可渲染的产品页面内容

### 第三阶段：国际化配置（1-2天）

#### 步骤3.1：next-i18next配置
- **文件路径：** `./next-i18next.config.js`
- **涉及内容：** 语言列表、默认语言、命名空间配置
- **代码行数：** 约20-30行
- **支持语言：** en, zh-CN, ja, es
- **预期结果：** 国际化路由正常工作

#### 步骤3.2：创建语言文件
- **文件路径：** 
  - `./locales/en/common.json`
  - `./locales/zh-CN/common.json`
  - `./locales/ja/common.json`
  - `./locales/es/common.json`
- **涉及内容：** UI文本翻译、导航菜单、按钮文本
- **代码行数：** 每个文件约100-150行
- **关键内容：**
  - 导航菜单文本
  - 按钮和表单标签
  - 错误消息
- **预期结果：** 多语言UI支持完整

#### 步骤3.3：路由国际化设置
- **文件路径：** `./src/app/[locale]/layout.tsx`
- **涉及内容：** 语言参数处理、元数据国际化
- **代码行数：** 约40-60行
- **关键功能：**
  - 语言参数验证
  - 动态元数据生成
  - 语言切换器
- **预期结果：** URL支持语言切换（/en, /zh-CN等）

### 第四阶段：核心页面开发（3-4天）

#### 步骤4.1：基础布局组件
- **文件路径：** 
  - `./src/components/layout/Header.tsx`
  - `./src/components/layout/Footer.tsx`
- **涉及内容：** 导航菜单、语言切换器、响应式布局
- **代码行数：** Header约80-100行，Footer约60-80行
- **关键功能：**
  - 响应式导航菜单
  - 语言切换下拉菜单
  - 企业联系信息
- **预期结果：** 统一的页面布局框架

#### 步骤4.2：UI组件库
- **文件路径：** 
  - `./src/components/ui/Button.tsx`
  - `./src/components/ui/Card.tsx`
  - `./src/components/ui/Input.tsx`
- **涉及内容：** 可复用的基础组件
- **代码行数：** 每个组件约30-50行
- **关键特性：**
  - Tailwind变体支持
  - TypeScript类型安全
  - 可访问性支持
- **预期结果：** 一致的设计系统

#### 步骤4.3：首页开发
- **文件路径：** `./src/app/[locale]/page.tsx`
- **涉及内容：** 英雄区域、产品概览、企业介绍
- **代码行数：** 约100-150行
- **关键区块：**
  - Hero Banner（企业形象展示）
  - 产品系列概览
  - 企业优势介绍
  - CTA按钮（联系我们）
- **预期结果：** 完整的首页展示

#### 步骤4.4：产品页面开发
- **文件路径：** 
  - `./src/app/[locale]/products/page.tsx`
  - `./src/app/[locale]/products/[slug]/page.tsx`
- **涉及内容：** 产品列表、产品详情、技术规格
- **代码行数：** 列表页约80-100行，详情页约120-150行
- **关键功能：**
  - 产品分类筛选
  - 产品卡片展示
  - 详细技术规格
  - 图片画廊
- **预期结果：** 产品展示功能完整

#### 步骤4.5：联系页面开发
- **文件路径：** `./src/app/[locale]/contact/page.tsx`
- **涉及内容：** 联系表单、公司信息
- **代码行数：** 约100-120行
- **关键功能：**
  - 询价表单
  - 表单验证
  - 公司联系信息
  - 地图集成（可选）
- **预期结果：** 客户询价功能完整

### 第五阶段：功能集成（2-3天）

#### 步骤5.1：邮件服务集成
- **文件路径：** 
  - `./src/app/api/contact/route.ts`
  - `./src/lib/email.ts`
- **涉及内容：** Resend API集成、邮件模板
- **代码行数：** API路由约60-80行，邮件库约40-60行
- **依赖库：** resend, zod
- **关键功能：**
  - 表单数据验证
  - 邮件模板渲染
  - 自动回复功能
- **预期结果：** 自动邮件回复功能正常

#### 步骤5.2：SEO优化
- **文件路径：** 各页面的metadata配置
- **涉及内容：** 动态元数据、结构化数据
- **代码行数：** 每个页面增加20-30行
- **关键配置：**
  - 页面标题和描述
  - Open Graph标签
  - 结构化数据（JSON-LD）
- **预期结果：** 搜索引擎优化完成

#### 步骤5.3：性能优化
- **文件路径：** `./next.config.js`, 图片优化配置
- **涉及内容：** 图片压缩、懒加载、缓存策略
- **关键配置：**
  - 图片自动优化
  - 静态资源缓存
  - 代码分割优化
- **预期结果：** 页面加载性能优化

## ⏱️ 时间安排

| 阶段 | 预计时间 | 关键里程碑 |
|------|----------|------------|
| 第一阶段 | 2-3天 | 开发环境就绪 |
| 第二阶段 | 2天 | 内容管理系统可用 |
| 第三阶段 | 1-2天 | 多语言功能完成 |
| 第四阶段 | 3-4天 | 核心页面完成 |
| 第五阶段 | 2-3天 | 功能集成完成 |
| **总计** | **10-14天** | **项目交付** |

## ⚠️ 风险评估与缓解措施

### 高风险项
1. **依赖兼容性问题**
   - 风险：Next.js 14与Contentlayer可能存在兼容问题
   - 缓解：在第一阶段仔细测试，准备备用方案

2. **国际化路由复杂性**
   - 风险：多语言路由配置可能出错
   - 缓解：先实现基础功能，再逐步添加国际化

### 中风险项
1. **邮件服务配置**
   - 风险：Resend API配置可能失败
   - 缓解：准备多个邮件服务提供商作为备选

2. **内容管理复杂性**
   - 风险：Contentlayer配置可能过于复杂
   - 缓解：准备直接使用MDX的简化方案

## ✅ 质量控制检查点

### 每阶段完成标准
- [ ] 功能验证通过
- [ ] TypeScript类型检查无错误
- [ ] 代码审查完成
- [ ] 单元测试覆盖关键功能
- [ ] 响应式设计测试通过

### 最终交付标准
- [ ] 所有页面正常渲染
- [ ] 多语言切换功能正常
- [ ] 联系表单邮件发送成功
- [ ] SEO元数据配置完整
- [ ] 性能指标达标（LCP < 2.5s）
- [ ] 可部署到Vercel生产环境

## 📦 交付物清单

1. **完整项目代码** - 包含所有源代码和配置文件
2. **开发环境** - 可本地运行的开发服务器
3. **示例内容** - 至少2个产品的多语言内容
4. **部署配置** - Vercel部署所需的配置文件
5. **项目文档** - 更新后的技术文档和使用说明
6. **环境变量模板** - .env.example文件

---

**创建时间：** 2025-06-14  
**计划执行：** 立即开始第一阶段  
**负责人：** Augment Agent  
**状态：** 待执行
